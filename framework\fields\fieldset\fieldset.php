<?php if ( ! defined( 'ABSPATH' ) ) { die; } // 禁止直接访问

/**
 * Fieldset 字段类
 *
 * 创建字段组容器，支持嵌套任意类型的子字段，提供可折叠、拖拽排序等高级功能
 *
 * @package Xun Framework
 * <AUTHOR>
 * @since   1.0
 */
if ( ! class_exists( 'XUN_Field_fieldset' ) ) {

    class XUN_Field_fieldset extends XUN_Fields {

        /**
         * 构造函数
         * 
         * 初始化字段组实例
         * 
         * @since 1.0
         * 
         * @param array  $field  字段配置数组
         * @param mixed  $value  字段值
         * @param string $unique 唯一标识符
         * @param string $where  字段位置
         * @param string $parent 父级字段
         */
        public function __construct( $field, $value = '', $unique = '', $where = '', $parent = '' ) {
            parent::__construct( $field, $value, $unique, $where, $parent );
        }

        /**
         * 渲染字段组
         *
         * 输出字段组的HTML结构，包含标题、子字段和控制按钮
         *
         * @since 1.0
         */
        public function render() {

            // 获取字段配置
            $args = wp_parse_args( $this->field, array(
                'title'         => '',
                'desc'          => '',
                'fields'        => array(),
                'collapsible'   => true,
                'collapsed'     => false,
                'sortable'      => false,
                'show_preview'  => true,
                'show_controls' => true,
                'card_style'    => true,
                'icon'          => 'dashicons-admin-settings',
                'before'        => '',
                'after'         => '',
            ) );

            // 获取字段值
            $value = $this->value;
            if ( ! is_array( $value ) ) {
                $value = array();
            }

            // 输出字段前置内容
            echo $this->field_before();

            // 开始 fieldset 容器
            echo '<div class="xun-fieldset-container" data-field-id="' . esc_attr( $this->field['id'] ) . '">';

            // 渲染字段组卡片
            $this->render_fieldset_card( $args, $value );

            echo '</div>'; // 关闭 fieldset 容器

            // 输出字段后置内容
            echo $this->field_after();
        }

        /**
         * 渲染字段组卡片
         *
         * 渲染现代化的卡片式字段组界面
         *
         * @since 1.0
         *
         * @param array $args  字段配置
         * @param array $value 字段值
         */
        private function render_fieldset_card( $args, $value ) {

            // 生成唯一ID
            $fieldset_id = 'xun-fieldset-' . uniqid();
            $is_collapsed = $args['collapsed'];

            // 获取统计信息
            $stats = $this->get_fieldset_stats( $args['fields'], $value );

            // 主卡片容器 - 简洁样式
            echo '<div class="xun-fieldset-card bg-white rounded-lg border border-gray-200 overflow-hidden transition-all duration-200 w-full" id="' . esc_attr( $fieldset_id ) . '" data-stats="' . esc_attr( json_encode( $stats ) ) . '">';

            // 渲染卡片头部
            if ( ! empty( $args['title'] ) || $args['show_controls'] ) {
                $this->render_card_header( $args, $is_collapsed, $stats );
            }

            // 渲染字段内容区域 - 使用更流畅的动画
            $content_class = 'xun-fieldset-content overflow-hidden transition-all duration-300 ease-out';
            $content_style = '';
            if ( $is_collapsed ) {
                $content_class .= ' xun-fieldset-collapsed';
                $content_style = ' style="max-height: 0; opacity: 0;"';
            } else {
                $content_class .= ' xun-fieldset-expanded';
                $content_style = ' style="max-height: none; opacity: 1;"';
            }

            echo '<div class="' . $content_class . '"' . $content_style . '>';

            // 描述信息（如果有的话，显示在内容区域顶部）
            if ( ! empty( $args['desc'] ) ) {
                echo '<div class="px-4 py-3 bg-blue-50 border-b border-blue-100">';
                echo '<p class="text-sm text-blue-700">' . wp_kses_post( $args['desc'] ) . '</p>';
                echo '</div>';
            }

            // 渲染子字段
            if ( ! empty( $args['fields'] ) ) {
                $this->render_fields_content( $args['fields'], $value );
            } else {
                $this->render_empty_state();
            }

            echo '</div>'; // 关闭内容区域

            echo '</div>'; // 关闭主卡片
        }

        /**
         * 渲染卡片头部
         *
         * 包含标题、图标、控制按钮等
         *
         * @since 1.0
         *
         * @param array $args        字段配置
         * @param bool  $is_collapsed 是否折叠状态
         * @param array $stats       统计信息
         */
        private function render_card_header( $args, $is_collapsed, $stats = array() ) {

            // 简洁的头部样式 - 整个头部可点击
            $header_class = 'xun-fieldset-header bg-white border-b border-gray-200 px-4 py-3 hover:bg-gray-50 transition-colors duration-150 cursor-pointer select-none';
            if ( $args['collapsible'] ) {
                $header_class .= ' xun-fieldset-clickable';
            }
            echo '<div class="' . $header_class . '" data-collapsed="' . ( $is_collapsed ? 'true' : 'false' ) . '">';

            // 添加响应式CSS
            echo '<style>
                @media (max-width: 640px) {
                    .xun-fieldset-toggle-icon,
                    .xun-fieldset-preview-btn {
                        width: 1.25rem !important;
                        height: 1.25rem !important;
                    }
                    .xun-fieldset-toggle-icon svg,
                    .xun-fieldset-preview-btn svg {
                        width: 0.875rem !important;
                        height: 0.875rem !important;
                    }
                    .xun-fieldset-header {
                        padding: 0.75rem 1rem !important;
                    }
                }
            </style>';

            // 单行布局 - 所有内容在一行内
            echo '<div class="flex items-center justify-between pointer-events-none">';

            // 左侧：折叠按钮 + 标题 + 状态
            echo '<div class="flex items-center space-x-3 min-w-0 flex-1">';

            // 折叠/展开按钮（移到最左侧）- 响应式尺寸
            if ( $args['collapsible'] ) {
                $toggle_class = $is_collapsed ? 'rotate-0' : 'rotate-90';
                echo '<div class="xun-fieldset-toggle-icon flex-shrink-0 inline-flex items-center justify-center w-6 h-6 text-gray-400">';
                echo '<svg class="w-4 h-4 transform transition-transform duration-300 ease-out ' . $toggle_class . '" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
                echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>';
                echo '</svg>';
                echo '</div>';
            }

            // 图标（可选，更小尺寸）
            if ( ! empty( $args['icon'] ) ) {
                echo '<div class="flex items-center justify-center w-5 h-5 flex-shrink-0">';
                if ( strpos( $args['icon'], 'dashicons-' ) === 0 ) {
                    echo '<span class="dashicons ' . esc_attr( $args['icon'] ) . ' text-gray-500 text-sm"></span>';
                } else {
                    echo '<svg class="w-4 h-4 text-gray-500" fill="currentColor" viewBox="0 0 20 20">';
                    echo '<path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"></path>';
                    echo '</svg>';
                }
                echo '</div>';
            }

            // 标题
            if ( ! empty( $args['title'] ) ) {
                echo '<h3 class="text-sm font-medium text-gray-900 truncate">' . esc_html( $args['title'] ) . '</h3>';
            }

            // 状态指示器（紧凑版本）
            if ( ! empty( $stats ) && $stats['total_fields'] > 0 ) {
                echo '<div class="flex items-center space-x-1 text-xs text-gray-500">';
                $completion_rate = $stats['completion_rate'];
                $status_class = 'bg-gray-400';
                if ( $completion_rate >= 100 ) {
                    $status_class = 'bg-green-500';
                } elseif ( $completion_rate >= 50 ) {
                    $status_class = 'bg-yellow-500';
                }
                echo '<div class="w-1.5 h-1.5 rounded-full ' . $status_class . '"></div>';
                echo '<span>(' . $stats['filled_fields'] . '/' . $stats['total_fields'] . ')</span>';
                echo '</div>';
            }

            echo '</div>'; // 关闭左侧

            // 右侧：其他控制按钮（如果有）
            if ( $args['show_controls'] && $args['show_preview'] ) {
                echo '<div class="flex items-center space-x-1 pointer-events-auto">';

                // 预览按钮 - 响应式尺寸，阻止事件冒泡
                echo '<button type="button" class="xun-fieldset-preview-btn inline-flex items-center justify-center w-6 h-6 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded transition-colors duration-150 touch-manipulation" title="预览字段组" onclick="event.stopPropagation();">';
                echo '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
                echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>';
                echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>';
                echo '</svg>';
                echo '</button>';

                echo '</div>'; // 关闭控制按钮
            }

            echo '</div>'; // 关闭 flex 容器
            echo '</div>'; // 关闭头部
        }

        /**
         * 渲染字段内容区域
         *
         * 渲染所有子字段
         *
         * @since 1.0
         *
         * @param array $fields 子字段配置
         * @param array $value  字段值
         */
        private function render_fields_content( $fields, $value ) {
            
            echo '<div class="xun-fieldset-fields p-3 sm:p-4 lg:p-6 space-y-3 sm:space-y-4 lg:space-y-6 bg-gray-50">';

            foreach ( $fields as $field ) {
                
                if ( empty( $field['id'] ) ) {
                    continue;
                }

                // 获取字段值
                $field_value = isset( $value[ $field['id'] ] ) ? $value[ $field['id'] ] : ( isset( $field['default'] ) ? $field['default'] : '' );

                // 生成字段名称
                $field_name = $this->field_name( '[' . $field['id'] . ']' );

                // 渲染单个字段容器 - 响应式间距
                echo '<div class="xun-fieldset-field bg-white rounded-md sm:rounded-lg border border-gray-200 p-3 sm:p-4 hover:border-gray-300 transition-colors duration-200">';

                // 使用统一的字段工厂方法渲染子字段
                $sub_unique = $this->unique . '[' . $this->field['id'] . ']';
                XUN::field( $field, $field_value, $sub_unique, 'fieldset', $this->field['id'] );

                echo '</div>';
            }

            echo '</div>'; // 关闭字段容器
        }

        /**
         * 渲染空状态
         *
         * 当没有子字段时显示的占位内容
         *
         * @since 1.0
         */
        private function render_empty_state() {
            
            echo '<div class="xun-fieldset-empty p-12 text-center bg-gray-50">';
            echo '<div class="max-w-sm mx-auto">';

            // 空状态图标
            echo '<div class="flex items-center justify-center w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full">';
            echo '<svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
            echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>';
            echo '</svg>';
            echo '</div>';

            echo '<h3 class="text-lg font-medium text-gray-900 mb-2">暂无字段</h3>';
            echo '<p class="text-sm text-gray-500">此字段组尚未配置任何子字段。请在配置中添加 fields 数组来定义子字段。</p>';

            echo '</div>';
            echo '</div>';
        }

        /**
         * 验证字段值
         *
         * 验证字段组中所有子字段的值
         *
         * @since 1.0
         *
         * @param mixed $value 要验证的值
         *
         * @return array 验证后的值
         */
        public function validate( $value ) {
            
            if ( ! is_array( $value ) ) {
                return array();
            }

            $validated = array();
            $fields = isset( $this->field['fields'] ) ? $this->field['fields'] : array();

            foreach ( $fields as $field ) {
                
                if ( empty( $field['id'] ) ) {
                    continue;
                }

                $field_id = $field['id'];
                $field_value = isset( $value[ $field_id ] ) ? $value[ $field_id ] : '';

                // 基本的数据清理
                if ( is_string( $field_value ) ) {
                    $field_value = sanitize_text_field( $field_value );
                } elseif ( is_array( $field_value ) ) {
                    $field_value = array_map( 'sanitize_text_field', $field_value );
                }

                $validated[ $field_id ] = $field_value;
            }

            return $validated;
        }

        /**
         * 获取字段组统计信息
         *
         * 返回字段组的统计数据，用于显示在头部
         *
         * @since 1.0
         *
         * @param array $fields 子字段配置
         * @param array $value  字段值
         *
         * @return array 统计信息
         */
        private function get_fieldset_stats( $fields, $value ) {

            $stats = array(
                'total_fields'    => count( $fields ),
                'filled_fields'   => 0,
                'empty_fields'    => 0,
                'completion_rate' => 0,
            );

            foreach ( $fields as $field ) {
                if ( empty( $field['id'] ) ) {
                    continue;
                }

                $field_value = isset( $value[ $field['id'] ] ) ? $value[ $field['id'] ] : '';

                if ( ! empty( $field_value ) ) {
                    $stats['filled_fields']++;
                } else {
                    $stats['empty_fields']++;
                }
            }

            if ( $stats['total_fields'] > 0 ) {
                $stats['completion_rate'] = round( ( $stats['filled_fields'] / $stats['total_fields'] ) * 100 );
            }

            return $stats;
        }

        /**
         * 渲染字段组状态指示器
         *
         * 在头部显示字段组的完成状态
         *
         * @since 1.0
         *
         * @param array $stats 统计信息
         */
        private function render_status_indicator( $stats ) {

            $completion_rate = $stats['completion_rate'];
            $status_class = 'bg-gray-200';
            $status_text = '未完成';

            if ( $completion_rate >= 100 ) {
                $status_class = 'bg-green-500';
                $status_text = '已完成';
            } elseif ( $completion_rate >= 50 ) {
                $status_class = 'bg-yellow-500';
                $status_text = '进行中';
            }

            echo '<div class="flex items-center space-x-1 sm:space-x-2 text-xs">';
            echo '<div class="flex items-center space-x-1">';
            echo '<div class="w-2 h-2 rounded-full ' . $status_class . '"></div>';
            echo '<span class="text-gray-600 hidden sm:inline">' . $status_text . '</span>';
            echo '</div>';
            echo '<span class="text-gray-500 text-xs">(' . $stats['filled_fields'] . '/' . $stats['total_fields'] . ')</span>';
            echo '</div>';
        }

        /**
         * 渲染进度条
         *
         * 显示字段组的完成进度
         *
         * @since 1.0
         *
         * @param int $completion_rate 完成率
         */
        private function render_progress_bar( $completion_rate ) {

            $progress_class = 'bg-gray-300';
            if ( $completion_rate >= 100 ) {
                $progress_class = 'bg-green-500';
            } elseif ( $completion_rate >= 50 ) {
                $progress_class = 'bg-yellow-500';
            } else {
                $progress_class = 'bg-red-500';
            }

            echo '<div class="w-full bg-gray-200 rounded-full h-1 sm:h-1.5 mt-1.5 sm:mt-2">';
            echo '<div class="' . $progress_class . ' h-1 sm:h-1.5 rounded-full transition-all duration-300" style="width: ' . $completion_rate . '%"></div>';
            echo '</div>';
        }

        /**
         * 生成字段组摘要
         *
         * 为预览功能生成字段组的摘要信息
         *
         * @since 1.0
         *
         * @param array $value 字段值
         *
         * @return string 摘要文本
         */
        private function generate_summary( $value ) {

            if ( empty( $value ) ) {
                return '暂无数据';
            }

            $summary_parts = array();
            $count = 0;

            foreach ( $value as $key => $val ) {
                if ( $count >= 3 ) {
                    break;
                }

                if ( ! empty( $val ) ) {
                    if ( is_string( $val ) && strlen( $val ) > 20 ) {
                        $val = substr( $val, 0, 20 ) . '...';
                    }
                    $summary_parts[] = $key . ': ' . $val;
                    $count++;
                }
            }

            if ( empty( $summary_parts ) ) {
                return '暂无数据';
            }

            $summary = implode( ', ', $summary_parts );
            if ( count( $value ) > 3 ) {
                $summary .= ' 等';
            }

            return $summary;
        }

        /**
         * 加载字段资源
         *
         * 加载字段组所需的CSS和JavaScript文件
         *
         * @since 1.0
         */
        public function enqueue() {

            // 加载JavaScript文件
            wp_enqueue_script(
                'xun-fieldset',
                XUN_Setup::$url . '/assets/js/fields/fieldset.js',
                array( 'jquery', 'jquery-ui-sortable' ),
                XUN_Setup::$version,
                true
            );

            // Fieldset 样式已移至 framework/assets/css/input.css

            // 传递本地化数据
            wp_localize_script( 'xun-fieldset', 'xunFieldset', array(
                'confirmDelete' => '确定要删除这个字段组吗？',
                'expandText'    => '展开',
                'collapseText'  => '折叠',
                'previewText'   => '预览',
                'copyText'      => '复制',
                'deleteText'    => '删除',
                'nonce'         => wp_create_nonce( 'xun_fieldset_nonce' ),
            ) );
        }
    }
}
