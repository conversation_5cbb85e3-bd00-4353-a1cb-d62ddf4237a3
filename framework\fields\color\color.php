<?php
/**
 * 现代化颜色选择器字段
 * 
 * 支持多种颜色格式、透明度、调色板、历史记录等高级功能
 * 
 * @package Xun Framework
 * <AUTHOR>
 * @since   1.0
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit; // 防止直接访问
}

if ( ! class_exists( 'XUN_Field_color' ) ) {

    class XUN_Field_color extends XUN_Fields {

        /**
         * 字段类型
         */
        public $type = 'color';

        /**
         * 构造函数
         *
         * 初始化颜色字段实例。
         *
         * @since 1.0
         *
         * @param array  $field  字段配置数组
         * @param mixed  $value  字段值
         * @param string $unique 唯一标识符
         * @param string $where  字段位置
         * @param string $parent 父级字段
         */
        public function __construct( $field, $value = '', $unique = '', $where = '', $parent = '' ) {
            parent::__construct( $field, $value, $unique, $where, $parent );
        }
        
        /**
         * 支持的颜色格式
         */
        private $supported_formats = array( 'hex', 'rgb', 'rgba', 'hsl', 'hsla' );
        
        /**
         * 默认调色板
         */
        private $default_palette = array(
            '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
            '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
            '#F8C471', '#82E0AA', '#F1948A', '#85C1E9', '#D7BDE2',
            '#A3E4D7', '#F9E79F', '#D5A6BD', '#AED6F1', '#A9DFBF'
        );
        
        /**
         * 渲染字段 - 响应式自适应设计
         */
        public function render() {
            $args = wp_parse_args( $this->field, array(
                'format'           => 'hex',
                'alpha'            => false,
                'palette'          => $this->default_palette,
                'history'          => false,
                'contrast_check'   => false,
                'eyedropper'       => false,
                'keyboard_support' => true,
                'width'            => '100%',
                'height'           => '300px'
            ) );

            // 生成唯一ID - 包含字段路径信息以避免冲突
            $unique_suffix = uniqid();
            $field_path = $this->unique ? $this->unique . '_' . $this->field['id'] : $this->field['id'];
            $field_id = 'xun-color-' . md5($field_path) . '-' . $unique_suffix;
            $input_id = $field_id . '-input';

            // 获取当前值
            $value = $this->value;
            $display_value = ! empty( $value ) ? $value : ( ! empty( $args['default'] ) ? $args['default'] : '#3B82F6' );

            // 输出字段前置内容
            echo $this->field_before();

            // 响应式颜色选择器容器 - 移动端优先设计
            echo '<div class="xun-color-field bg-white rounded-lg border border-gray-200 p-3 sm:p-4 md:p-6 w-full" data-field-id="' . esc_attr( $field_id ) . '" data-field-path="' . esc_attr( $field_path ) . '">';

            // 响应式颜色预览和输入区域 - 使用内联样式确保兼容性
            echo '<div class="xun-color-responsive-container" style="display: flex; align-items: center; gap: 1rem; flex-wrap: wrap;">';

            // 添加响应式CSS
            echo '<style>
                @media (max-width: 768px) {
                    .xun-color-responsive-container {
                        flex-direction: column !important;
                        align-items: stretch !important;
                        gap: 0.75rem !important;
                    }
                    .xun-color-input-group {
                        flex-direction: column !important;
                        gap: 0.5rem !important;
                    }
                }
            </style>';

            // 颜色预览块 - 固定尺寸
            echo '<div style="flex-shrink: 0;">';
            echo '<div class="w-12 h-12 rounded-lg border-2 border-gray-300 shadow-sm cursor-pointer transition-transform duration-200 hover:scale-105" ';
            echo 'style="background: ' . esc_attr( $display_value ) . ';" ';
            echo 'id="' . esc_attr( $field_id ) . '-preview" ';
            echo 'title="' . esc_attr( __( '点击选择颜色', 'xun' ) ) . '">';
            echo '</div>';
            echo '</div>';

            // 颜色输入区域 - 水平布局
            echo '<div class="xun-color-input-group" style="display: flex; align-items: center; gap: 0.75rem; flex: 1; min-width: 0;">';

            // 颜色值输入框
            echo '<input type="text" ';
            echo 'name="' . esc_attr( $this->field_name() ) . '" ';
            echo 'id="' . esc_attr( $input_id ) . '" ';
            echo 'value="' . esc_attr( $value ) . '" ';
            echo 'class="xun-color-input flex-1 px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono transition-colors" ';
            echo 'style="min-width: 120px;" ';
            echo 'placeholder="' . esc_attr( __( '输入颜色值', 'xun' ) ) . '" ';
            echo $this->field_attributes() . '/>';

            // 格式选择器
            echo '<select class="xun-color-format-select px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">';
            $formats = array( 'hex' => 'HEX', 'rgb' => 'RGB', 'rgba' => 'RGBA', 'hsl' => 'HSL', 'hsla' => 'HSLA' );
            foreach ( $formats as $format_key => $format_label ) {
                $selected = $format_key === $args['format'] ? ' selected' : '';
                echo '<option value="' . esc_attr( $format_key ) . '"' . $selected . '>' . esc_html( $format_label ) . '</option>';
            }
            echo '</select>';

            echo '</div>';

            echo '</div>'; // 关闭flex容器

            // 响应式调色板 - 使用内联样式确保兼容性
            if ( ! empty( $args['palette'] ) && is_array( $args['palette'] ) ) {
                echo '<div class="mt-4">';
                echo '<label class="block text-xs font-medium text-gray-700 mb-2">' . esc_html( __( '调色板', 'xun' ) ) . '</label>';

                // 响应式网格布局 - 使用CSS Grid和媒体查询
                echo '<div class="xun-palette-grid" style="display: grid; grid-template-columns: repeat(10, 1fr); gap: 6px;">';

                // 添加调色板响应式CSS
                echo '<style>
                    @media (max-width: 768px) {
                        .xun-palette-grid {
                            grid-template-columns: repeat(6, 1fr) !important;
                            gap: 8px !important;
                        }
                    }
                    @media (min-width: 769px) and (max-width: 1024px) {
                        .xun-palette-grid {
                            grid-template-columns: repeat(8, 1fr) !important;
                        }
                    }
                    @media (min-width: 1025px) {
                        .xun-palette-grid {
                            grid-template-columns: repeat(12, 1fr) !important;
                        }
                    }
                </style>';

                foreach ( $args['palette'] as $palette_color ) {
                    echo '<button type="button" class="xun-palette-color xun-palette-' . esc_attr( $field_id ) . ' w-6 h-6 rounded border border-gray-300 hover:scale-110 hover:shadow-md transition-all duration-150 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1" ';
                    echo 'style="background: ' . esc_attr( $palette_color ) . ';" ';
                    echo 'data-color="' . esc_attr( $palette_color ) . '" ';
                    echo 'data-field-id="' . esc_attr( $field_id ) . '" ';
                    echo 'title="' . esc_attr( $palette_color ) . '">';
                    echo '</button>';
                }

                echo '</div>';
                echo '</div>';
            }

            // 颜色选择器配置数据 - 包含响应式配置
            $config = array(
                'fieldId'         => $field_id,
                'inputId'         => $input_id,
                'actualFieldId'   => $field_id, // 使用生成的唯一字段ID
                'fieldPath'       => $field_path, // 添加字段路径信息
                'value'           => $display_value,
                'format'          => $args['format'],
                'alpha'           => $args['alpha'],
                'palette'         => $args['palette'],
                'responsive'      => true, // 标记为响应式字段
                'messages'        => array(
                    'formatLabel'     => __( '格式', 'xun' ),
                    'invalidColor'    => __( '无效的颜色值', 'xun' ),
                    'colorCopied'     => __( '颜色已复制', 'xun' ),
                    'selectColor'     => __( '选择颜色', 'xun' ),
                    'closeColorPicker' => __( '关闭颜色选择器', 'xun' )
                )
            );

            echo '<script type="application/json" class="xun-color-config">' . wp_json_encode( $config ) . '</script>';

            echo '</div>'; // 关闭主容器

            // 输出字段后置内容
            echo $this->field_after();
        }
        
        /**
         * 获取容器CSS类
         */
        private function get_container_classes( $args ) {
            $classes = array( 'xun-color-field' );

            // 基础样式
            $classes[] = 'bg-white rounded-lg border border-gray-200 overflow-hidden';
            $classes[] = 'transition-all duration-200 ease-in-out';
            
            // 响应式容器样式
            $classes[] = 'w-full';
            
            // 移动端优化：确保触摸友好
            $classes[] = 'touch-manipulation';
            
            // 响应式内边距
            $classes[] = 'p-3 sm:p-4';

            return implode( ' ', $classes );
        }
        
        /**
         * 渲染工具栏
         */
        private function render_toolbar( $args, $field_id ) {
            // 左侧工具
            echo '<div class="flex items-center space-x-2">';
            echo '<button type="button" class="xun-color-eyedropper-btn inline-flex items-center px-3 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors" title="' . esc_attr__( '取色器', 'xun' ) . '">';
            echo '<svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM7 3V1m0 18v2m8-10h2m-2 0h2m-2 0v2m-2-2h2"></path></svg>';
            echo '<span>' . esc_html__( '取色', 'xun' ) . '</span>';
            echo '</button>';

            echo '<button type="button" class="xun-color-copy-btn inline-flex items-center px-3 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors" title="' . esc_attr__( '复制颜色', 'xun' ) . '">';
            echo '<svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path></svg>';
            echo '<span>' . esc_html__( '复制', 'xun' ) . '</span>';
            echo '</button>';
            echo '</div>';

            // 右侧工具
            echo '<div class="flex items-center space-x-2">';
            echo '<button type="button" class="xun-color-reset-btn inline-flex items-center px-3 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors" title="' . esc_attr__( '重置为默认值', 'xun' ) . '">';
            echo '<svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg>';
            echo '<span>' . esc_html__( '重置', 'xun' ) . '</span>';
            echo '</button>';
            echo '</div>';
        }
        
        /**
         * 渲染状态栏
         */
        private function render_status_bar( $args, $field_id ) {
            echo '<div class="xun-color-status-bar flex items-center justify-between p-2 bg-gray-50 border-t border-gray-200 text-xs text-gray-600">';
            
            // 左侧：当前颜色值
            echo '<div class="flex items-center gap-2">';
            echo '<span class="xun-color-current-value font-mono"></span>';
            echo '</div>';
            
            // 右侧：对比度信息（如果启用）
            if ( ! empty( $args['contrast_check'] ) ) {
                echo '<div class="flex items-center gap-2">';
                echo '<span>' . esc_html__( '对比度:', 'xun' ) . '</span>';
                echo '<span class="xun-color-contrast-ratio font-mono"></span>';
                echo '<span class="xun-color-contrast-status"></span>';
                echo '</div>';
            }
            
            echo '</div>';
        }
        
        /**
         * 字段脚本和样式
         */
        public function enqueue() {
            // 脚本依赖
            $dependencies = array( 'jquery' );

            // 注册并加载颜色选择器脚本
            wp_enqueue_script(
                'xun-color-field',
                XUN_Setup::$url . '/assets/js/fields/color.js',
                $dependencies,
                XUN_Setup::$version,
                true
            );
            
            // 本地化脚本
            wp_localize_script( 'xun-color-field', 'xunColor', array(
                'messages' => array(
                    'loading'         => __( '正在加载颜色选择器...', 'xun' ),
                    'error'           => __( '颜色选择器加载失败', 'xun' ),
                    'invalidColor'    => __( '无效的颜色值', 'xun' ),
                    'colorCopied'     => __( '颜色已复制到剪贴板', 'xun' ),
                    'contrastGood'    => __( '对比度良好', 'xun' ),
                    'contrastPoor'    => __( '对比度不足', 'xun' ),
                    'eyedropperError' => __( '您的浏览器不支持取色器功能', 'xun' )
                ),
                'nonce' => wp_create_nonce( 'xun_color_field' )
            ) );
        }
        
        /**
         * 字段验证
         */
        public function validate( $value ) {
            if ( empty( $value ) ) {
                return '';
            }
            
            // 验证颜色格式
            if ( $this->is_valid_color( $value ) ) {
                return sanitize_text_field( $value );
            }
            
            return '';
        }
        
        /**
         * 验证颜色值是否有效
         */
        private function is_valid_color( $color ) {
            // HEX格式验证
            if ( preg_match( '/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3}|[A-Fa-f0-9]{8})$/', $color ) ) {
                return true;
            }
            
            // RGB/RGBA格式验证
            if ( preg_match( '/^rgba?\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*(?:,\s*[0-1]?(?:\.\d+)?)?\s*\)$/', $color ) ) {
                return true;
            }
            
            // HSL/HSLA格式验证
            if ( preg_match( '/^hsla?\(\s*\d+\s*,\s*\d+%\s*,\s*\d+%\s*(?:,\s*[0-1]?(?:\.\d+)?)?\s*\)$/', $color ) ) {
                return true;
            }
            
            // 透明值
            if ( $color === 'transparent' ) {
                return true;
            }
            
            return false;
        }
    }
}
